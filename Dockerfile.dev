# 开发环境Dockerfile - 使用Ubuntu 24.04作为基础镜像，指定x86_64架构
FROM --platform=linux/amd64 ubuntu:24.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV GO_VERSION=1.23.11
ENV NODE_VERSION=20

# 更新包管理器并安装基础依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    ca-certificates \
    gnupg \
    lsb-release \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# 安装Go
RUN wget https://go.dev/dl/go${GO_VERSION}.linux-amd64.tar.gz \
    && tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz \
    && rm go${GO_VERSION}.linux-amd64.tar.gz

# 安装Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - \
    && apt-get install -y nodejs

# 设置Go环境变量
ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/go"
ENV GOBIN="/go/bin"
ENV PATH="${GOBIN}:${PATH}"

# 安装Air（Go热重载工具）
RUN go install github.com/air-verse/air@latest

# 创建工作目录
WORKDIR /app

# 复制Go模块文件
COPY go.mod go.sum ./

# 下载Go依赖
RUN go mod download

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装Node.js依赖（包括开发依赖）
RUN npm ci

# 创建数据目录
RUN mkdir -p /app/data

# 暴露端口
EXPOSE 9090

# 开发模式启动命令（会被docker-compose覆盖）
CMD ["npm", "run", "dev"]
